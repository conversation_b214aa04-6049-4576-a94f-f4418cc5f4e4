/*
 * 按键处理库实现文件
 * 支持单按键、组合按键、长按、连击等功能
 * 使用状态机处理按键事件，支持环形缓冲区存储按键结果
 */

#include "bits_button.h"
#include "string.h"
#include <stdatomic.h>

// static struct button_obj_t* button_list_head = NULL;
#define POPCOUNT_TYPE_T button_mask_type_t

// 按键实体对象，全局唯一
static bits_button_t bits_btn_entity;

#ifdef BITS_BTN_BUFFER_SIZE
// 环形缓冲区结构体，用于存储按键结果
typedef struct
{
    bits_btn_result_t buffer[BITS_BTN_BUFFER_SIZE];  // 缓冲区数组
    atomic_size_t read_idx;   // 原子读索引
    atomic_size_t write_idx;  // 原子写索引
} bits_btn_ring_buffer_t;

static atomic_size_t overwrite_count = 0;           // 覆写计数器
static bits_btn_debug_printf_func debug_printf = NULL;  // 调试打印函数指针
static bits_btn_ring_buffer_t ring_buffer;          // 环形缓冲区实例

// 从缓冲区读取按键结果的静态函数声明
static bool bits_btn_read_buffer(bits_btn_result_t *result);
#endif

// 调试打印二进制数的静态函数声明
static void debug_print_binary(key_value_type_t num);

/**
  * @brief  根据按键ID在按键数组中查找按键对象的索引
  *         这是一个内部使用的辅助函数，用于将按键ID映射到通过 `bits_button_init()`
  *         初始化的按键数组中对应的索引位置
  *
  * @param  key_id: 要查找的按键的唯一标识符
  *
  * @retval 如果找到则返回按键在数组中的索引(0到N-1)，如果按键ID无效则返回-1
  */
static int _get_btn_index_by_key_id(uint16_t key_id)
{
    bits_button_t *button = &bits_btn_entity;
    // 遍历所有按键，查找匹配的按键ID
    for (size_t i = 0; i < button->btns_cnt; i++)
    {
        if (button->btns[i].key_id == key_id)
        {
            return i;  // 找到匹配的按键，返回索引
        }
    }

    return -1;  // 未找到匹配的按键ID
}

/**
 * @brief  获取当前按键时钟计数
 * @retval 返回当前的按键时钟计数值
 */
static uint32_t get_button_tick(void)
{
    return bits_btn_entity.btn_tick;
}

#ifdef BITS_BTN_BUFFER_SIZE

/**
  * @brief  初始化按键结果的环形缓冲区
  * @retval 无
  */
static void bits_btn_init_buffer(void)
{
    bits_btn_ring_buffer_t *buf = &ring_buffer;

    // 初始化原子变量
    atomic_init(&buf->read_idx, 0);
    atomic_init(&buf->write_idx, 0);
    atomic_init(&overwrite_count, 0);
}

/**
 * @brief  检查缓冲区是否为空
 * @retval true: 缓冲区为空，false: 缓冲区不为空
 */
bool bits_btn_is_buffer_empty(void)
{
    bits_btn_ring_buffer_t *buf = &ring_buffer;

    // 原子读取读写索引
    size_t current_read = atomic_load_explicit(&buf->read_idx, memory_order_relaxed);
    size_t current_write = atomic_load_explicit(&buf->write_idx, memory_order_relaxed);
    return current_read == current_write;  // 读写索引相等表示缓冲区为空
}

/**
 * @brief  检查缓冲区是否已满
 * @retval true: 缓冲区已满，false: 缓冲区未满
 */
bool bits_btn_is_buffer_full(void)
{
    bits_btn_ring_buffer_t *buf = &ring_buffer;

    // 原子读取读写索引
    size_t current_write = atomic_load_explicit(&buf->write_idx, memory_order_relaxed);
    size_t current_read = atomic_load_explicit(&buf->read_idx, memory_order_relaxed);
    size_t next_write = (current_write + 1) % BITS_BTN_BUFFER_SIZE;  // 计算下一个写位置
    return next_write == current_read;  // 下一个写位置等于读位置表示缓冲区已满
}

/**
 * @brief  获取缓冲区中的数据数量
 * @retval 缓冲区中的数据数量
 */
size_t get_bits_btn_buffer_count(void)
{
    bits_btn_ring_buffer_t *buf = &ring_buffer;
    size_t current_write = atomic_load_explicit(&buf->write_idx, memory_order_relaxed);
    size_t current_read = atomic_load_explicit(&buf->read_idx, memory_order_relaxed);

    // 计算缓冲区中的数据数量
    if (current_write >= current_read)
    {
        return current_write - current_read;  // 写索引大于等于读索引的情况
    }
    else
    {
        // 写索引小于读索引，说明发生了环绕
        return BITS_BTN_BUFFER_SIZE - current_read + current_write;
    }
}

/**
  * @brief  清空环形缓冲区。注意：在多线程环境中需要额外的同步机制
  * @retval 无
  */
void bits_btn_clear_buffer(void)
{
    bits_btn_ring_buffer_t *buf = &ring_buffer;

    // 原子操作重置读写索引为0
    atomic_store_explicit(&buf->read_idx, 0, memory_order_relaxed);
    atomic_store_explicit(&buf->write_idx, 0, memory_order_relaxed);
}

/**
 * @brief  获取缓冲区覆写次数
 * @retval 返回缓冲区被覆写的次数
 */
size_t get_bits_btn_overwrite_count(void)
{
    return atomic_load_explicit(&overwrite_count, memory_order_relaxed);
}

/**
  * @brief  向环形缓冲区写入按键结果
  * @param  result: 指向要写入的按键结果的指针
  * @retval 写入成功返回true，缓冲区已满返回false
  */
bool bits_btn_write_buffer(bits_btn_result_t *result)
{
    bits_btn_ring_buffer_t *buf = &ring_buffer;

    if(result == NULL)
        return false;

    // 原子读取当前写索引和读索引
    size_t current_write = atomic_load_explicit(&buf->write_idx, memory_order_relaxed);
    size_t current_read = atomic_load_explicit(&buf->read_idx, memory_order_consume);

    // 计算下一个写位置（使用模运算实现环形）
    size_t next_write = (current_write + 1) % BITS_BTN_BUFFER_SIZE;

    if (next_write == current_read) {  // 缓冲区已满
        atomic_fetch_add_explicit(&overwrite_count, 1, memory_order_relaxed);
        return false;
    }

    // 写入数据到缓冲区
    buf->buffer[current_write] = *result;

    // 更新写索引（确保数据对其他线程可见）
    atomic_store_explicit(&buf->write_idx, next_write, memory_order_release);
    return true;
}

/**
  * @brief  在单写入者场景下向环形缓冲区写入按键结果（支持覆写）
  * @param  result: 指向要写入的按键结果的指针
  * @retval 写入成功返回true
  */
static bool bits_btn_write_buffer_overwrite(bits_btn_result_t *result)
{
    bits_btn_ring_buffer_t *buf = &ring_buffer;

    if(result == NULL)
        return false;
    // 获取当前写位置
    size_t current_write = atomic_load_explicit(&buf->write_idx, memory_order_relaxed);
    size_t next_write = (current_write + 1) % BITS_BTN_BUFFER_SIZE;

    // 获取当前读位置（确保看到最新值）
    size_t current_read = atomic_load_explicit(&buf->read_idx, memory_order_consume);

    // 写入数据到缓冲区
    buf->buffer[current_write] = *result;

    // 当缓冲区满时，推进读指针
    if (next_write == current_read) {
        atomic_fetch_add_explicit(&overwrite_count, 1, memory_order_relaxed);
        size_t new_read = (current_read + 1) % BITS_BTN_BUFFER_SIZE;

        atomic_store_explicit(&buf->read_idx, new_read, memory_order_release);
    }

    // 更新写指针（确保数据在索引更新前可见）
    atomic_store_explicit(&buf->write_idx, next_write, memory_order_release);
    return true;
}

/**
  * @brief  从环形缓冲区读取按键结果
  * @param  result: 指向存储读取的按键结果的指针
  * @retval 读取成功返回true，缓冲区为空返回false
  */
static bool bits_btn_read_buffer(bits_btn_result_t *result)
{
    bits_btn_ring_buffer_t *buf = &ring_buffer;

    // 原子读取写索引和读索引
    size_t current_write = atomic_load_explicit(&buf->write_idx, memory_order_acquire);
    size_t current_read = atomic_load_explicit(&buf->read_idx, memory_order_relaxed);

    if (current_read == current_write) {  // 缓冲区为空
        return false;
    }

    // 从缓冲区读取数据
    *result = buf->buffer[current_read];

    // 更新读索引（使用模运算）
    atomic_store_explicit(&buf->read_idx, (current_read + 1) % BITS_BTN_BUFFER_SIZE, memory_order_release);

    return true;
}
#endif

/**
  * @brief  在初始化时对组合按键进行排序（按按键数量降序排列）
  * @param  button: 指向按键对象的指针
  * @retval 无
  */
 static void sort_combo_buttons_in_init(bits_button_t *button)
 {
    const uint16_t cnt = button->btns_combo_cnt;  // 获取组合按键数量

    // 如果没有组合按键或只有一个，跳过排序
    if (cnt <= 1)
    {
        if (debug_printf && cnt == 0) debug_printf("No combo buttons\n");
        return;
    }

    // 初始化索引数组 (0,1,2,...)
    for (uint16_t i = 0; i < cnt; i++)
        button->combo_sorted_indices[i] = i;

    // 插入排序（按按键数量降序排列）
    for (uint16_t i = 1; i < cnt; i++)
    {
        const uint16_t temp_idx = button->combo_sorted_indices[i];  // 当前要插入的元素
        const uint8_t temp_keys = button->btns_combo[temp_idx].key_count;  // 当前元素的按键数量
        int16_t j = i - 1;  // 从已排序部分的末尾开始

        // 查找插入位置：按键数量多的优先级高
        while (j >= 0 &&
               button->btns_combo[button->combo_sorted_indices[j]].key_count < temp_keys)
        {
            // 将优先级低的元素向后移动
            button->combo_sorted_indices[j + 1] = button->combo_sorted_indices[j];
            j--;
        }
        // 在正确位置插入当前元素
        button->combo_sorted_indices[j + 1] = temp_idx;
    }

#if 0
    // 调试输出排序结果
    if (debug_printf)
    {
        debug_printf("Sorted combo indices (%d):\n", cnt);
        for (uint16_t i = 0; i < cnt; i++)
        {
            const button_obj_combo_t* c = &button->btns_combo[button->combo_sorted_indices[i]];
            debug_printf("  %d: ID=%d, Keys=", i, c->btn.key_id);
            for (uint8_t j = 0; j < c->key_count; j++)
                debug_printf("%d ", c->key_single_ids[j]);
            debug_printf("\n");
        }
    }
#endif
}

/**
 * @brief  初始化按键系统
 * @param  btns: 单按键数组指针
 * @param  btns_cnt: 单按键数量
 * @param  btns_combo: 组合按键数组指针
 * @param  btns_combo_cnt: 组合按键数量
 * @param  read_button_level_func: 读取按键电平的函数指针
 * @param  bits_btn_result_cb: 按键结果回调函数指针
 * @param  bis_btn_debug_printf: 调试打印函数指针
 * @retval 0: 成功，-1: 按键ID无效，-2: 参数无效，-3: 组合按键数量超限
 */
int32_t bits_button_init(button_obj_t* btns                                     , \
                         uint16_t btns_cnt                                      , \
                         button_obj_combo_t *btns_combo                         , \
                         uint16_t btns_combo_cnt                                , \
                         bits_btn_read_button_level read_button_level_func      , \
                         bits_btn_result_callback bits_btn_result_cb            , \
                         bits_btn_debug_printf_func bis_btn_debug_printf          \
                 )
{
    bits_button_t *button = &bits_btn_entity;
    debug_printf = bis_btn_debug_printf;

    // 参数有效性检查
    if (btns == NULL|| read_button_level_func == NULL ||
        (btns_combo_cnt > 0 && btns_combo == NULL))
    {
        if(debug_printf)
            debug_printf("Invalid init parameters !\n");
        return -2;
    }

    // 清零按键实体对象
    memset(button, 0, sizeof(bits_button_t));

    // 初始化按键实体对象的成员
    button->btns = btns;
    button->btns_cnt = btns_cnt;
    button->btns_combo = btns_combo;
    button->btns_combo_cnt = btns_combo_cnt;
    button->_read_button_level = read_button_level_func;
    button->bits_btn_result_cb = bits_btn_result_cb;

    // 检查组合按键数量是否超限
    if (btns_combo_cnt > BITS_BTN_MAX_COMBO_BUTTONS)
    {
        if (debug_printf)
        {
            debug_printf("Error: Too many combo buttons (%d > max %d)\n",
                         btns_combo_cnt, BITS_BTN_MAX_COMBO_BUTTONS);
        }
        return -3;
    }

    // 初始化组合按键的掩码
    for(uint16_t i = 0; i < btns_combo_cnt; i++)
    {
        button_obj_combo_t *combo = &button->btns_combo[i];
        combo->combo_mask = 0;

        // 为每个组合按键计算掩码
        for(uint16_t j = 0; j < combo->key_count; j++)
        {
            int idx = _get_btn_index_by_key_id(combo->key_single_ids[j]);
            if (idx == -1)
            {
                if(debug_printf)
                    debug_printf("Error, get_btn_index failed! \n");
                return -1; // 无效的按键ID
            }
            combo->combo_mask |= ((button_mask_type_t)1UL << idx);
        }
    }

    // 在初始化时对组合按键进行排序
    sort_combo_buttons_in_init(button);

#ifdef BITS_BTN_BUFFER_SIZE
    // 初始化环形缓冲区
    bits_btn_init_buffer();
#endif
    return 0;
}

/**
  * @brief  从缓冲区获取按键结果
  * @param  result: 指向存储按键结果的指针
  * @retval 读取成功返回true，缓冲区为空返回false
  */
bool bits_button_get_key_result(bits_btn_result_t *result)
{
    return bits_btn_read_buffer(result);
}

/**
  * @brief  重置所有按键状态为空闲状态
  *         此函数应在从低功耗模式恢复时调用，
  *         以清除暂停前的任何残留按键状态
  * @retval 无
  */
void bits_button_reset_states(void)
{
    bits_button_t *button = &bits_btn_entity;

    if (debug_printf)
        debug_printf("Resetting all button states\n");

    // 重置所有单按键状态
    for (size_t i = 0; i < button->btns_cnt; i++)
    {
        button->btns[i].current_state = BTN_STATE_IDLE;
        button->btns[i].last_state = BTN_STATE_IDLE;
        button->btns[i].state_bits = 0;
        button->btns[i].state_entry_time = 0;
        button->btns[i].long_press_period_trigger_cnt = 0;
    }

    // 重置所有组合按键状态
    if (button->btns_combo != NULL && button->btns_combo_cnt > 0)
    {
        for (size_t i = 0; i < button->btns_combo_cnt; i++)
        {
            button_obj_combo_t *combo = &button->btns_combo[i];
            combo->btn.current_state = BTN_STATE_IDLE;
            combo->btn.last_state = BTN_STATE_IDLE;
            combo->btn.state_bits = 0;
            combo->btn.state_entry_time = 0;
            combo->btn.long_press_period_trigger_cnt = 0;
        }
    }

    // 重置全局按键状态并强制掩码同步
    // 这可以防止重置后出现虚假的释放事件
    button_mask_type_t current_physical_mask = 0;
    for(size_t i = 0; i < button->btns_cnt; i++)
    {
        uint8_t read_gpio_level = button->_read_button_level(&button->btns[i]);
        if (read_gpio_level == button->btns[i].active_level)
        {
            current_physical_mask |= ((button_mask_type_t)1UL << i);
        }
    }

    button->current_mask = current_physical_mask;
    button->last_mask = current_physical_mask;
    button->state_entry_time = get_button_tick();

#ifdef BITS_BTN_BUFFER_SIZE
    // 清空事件缓冲区
    bits_btn_clear_buffer();
#endif
}

/**
  * @brief  在数字末尾添加一个位
  * @param  state_bits: 源数字指针
  * @param  bit: 目标位
  * @retval 无
  */
static void __append_bit(state_bits_type_t* state_bits, uint8_t bit)
{
    *state_bits = (*state_bits << 1) | bit;
}

/**
  * @brief  检查数字是否匹配目标位模式
  * @param  state_bits: 源数字指针
  * @param  target: 目标位模式
  * @param  target_bits_number: 目标位数量
  * @retval 匹配返回1，否则返回0
  */
static uint8_t __check_if_the_bits_match(const key_value_type_t *state_bits, key_value_type_t target, uint8_t target_bits_number)
{
    key_value_type_t mask = (1 << target_bits_number) - 1;

    return (((*state_bits) & mask) == target? 1 : 0);
}

#if 0
// 检查是否为重复点击模式的函数（已注释）
uint8_t check_is_repeat_click_mode(struct button_obj_t* button)
{
    key_value_type_t kv_input = button->key_value;

    /* 检查最低两位是否为 0b10 */
    if((kv_input & 0b11) != 0b10)
        return 0;

    /* 计算异或结果 */
    key_value_type_t xor_result = kv_input ^ (kv_input >> 1);

    /* 检查 xor_result + 1 是否为2的幂
       这意味着除了最低位外的所有位都是1 */
    return (xor_result != 0) && (((xor_result + 1) & (xor_result - 1)) == 0);
}
#endif

/**
  * @brief  报告按键事件
  * @param  button: 指向按键对象的指针
  * @param  result: 指向要报告的按键结果的指针
  * @retval 无
  */
static void bits_btn_report_event(struct button_obj_t* button, bits_btn_result_t *result)
{
    bits_btn_result_callback btn_result_cb = bits_btn_entity.bits_btn_result_cb;

    if(result == NULL) return;

    // 调试打印按键事件信息
    if(debug_printf)
        debug_printf("key id[%d],event:%d, long trigger_cnt:%d, key_value:", result->key_id, result->event ,result->long_press_period_trigger_cnt);
    debug_print_binary(result->key_value);

#ifdef BITS_BTN_BUFFER_SIZE
    // 如果不是释放事件，则写入缓冲区（支持覆写）
    if(result->event != BTN_STATE_RELEASE)
        bits_btn_write_buffer_overwrite(result);
#endif

    // 如果有回调函数，则调用回调函数
    if(btn_result_cb)
        btn_result_cb(button, *result);

}

/**
  * @brief  更新按键状态机
  * @param  button: 指向按键对象的指针
  * @param  btn_pressed: 指示按键是否被按下的标志
  * @retval 无
  */
static void update_button_state_machine(struct button_obj_t* button, uint8_t btn_pressed)
{
    uint32_t current_time = get_button_tick();
    uint32_t time_diff = current_time - button->state_entry_time;
    bits_btn_result_t result = {0};
    result.key_id = button->key_id;

    if(button->param == NULL)
        return;

    switch (button->current_state)
    {
        case BTN_STATE_IDLE:  // 空闲状态
            if (btn_pressed)
            {
                __append_bit(&button->state_bits, 1);  // 添加按下位

                button->current_state = BTN_STATE_PRESSED;
                button->state_entry_time = current_time;

                result.key_value = button->state_bits;
                result.event = button->current_state;
                bits_btn_report_event(button, &result);
            }
            break;
        case BTN_STATE_PRESSED:  // 按下状态
            if (time_diff * BITS_BTN_TICKS_INTERVAL > button->param->long_press_start_time_ms)
            {
                __append_bit(&button->state_bits, 1);  // 添加长按位

                button->current_state = BTN_STATE_LONG_PRESS;
                button->state_entry_time = current_time;
                button->long_press_period_trigger_cnt = 0;

                result.key_value = button->state_bits;
                result.event = button->current_state;
                bits_btn_report_event(button, &result);
            }
            else if (btn_pressed == 0)  // 按键释放
            {
                button->current_state = BTN_STATE_RELEASE;
            }
            break;
        case BTN_STATE_LONG_PRESS:  // 长按状态
            if (btn_pressed == 0)  // 按键释放
            {
                button->long_press_period_trigger_cnt = 0;
                button->current_state = BTN_STATE_RELEASE;
            }
            else if(time_diff * BITS_BTN_TICKS_INTERVAL > button->param->long_press_period_triger_ms)
            {
                button->state_entry_time = current_time;
                button->long_press_period_trigger_cnt++;

                // 检查是否匹配特定的位模式
                if(__check_if_the_bits_match(&button->state_bits, 0b011, 3))
                {
                    __append_bit(&button->state_bits, 1);
                }

                result.key_value = button->state_bits;
                result.event = button->current_state;
                result.long_press_period_trigger_cnt = button->long_press_period_trigger_cnt;
                bits_btn_report_event(button, &result);
            }
            break;
        case BTN_STATE_RELEASE:  // 释放状态
            __append_bit(&button->state_bits, 0);  // 添加释放位

            result.key_value = button->state_bits;
            result.event = BTN_STATE_RELEASE;
            bits_btn_report_event(button, &result);

            button->current_state = BTN_STATE_RELEASE_WINDOW;
            button->state_entry_time = current_time;

            break;
        case BTN_STATE_RELEASE_WINDOW:  // 释放窗口状态
            if (btn_pressed)  // 在窗口期内再次按下
            {
                button->current_state = BTN_STATE_IDLE;
                button->state_entry_time = current_time;
            }
            else if (time_diff * BITS_BTN_TICKS_INTERVAL > button->param->time_window_time_ms)
            {
                // 时间窗口超时，触发事件并返回空闲状态
                button->current_state = BTN_STATE_FINISH;
            }
            break;
        case BTN_STATE_FINISH:  // 完成状态

            result.key_value = button->state_bits;
            result.event = BTN_STATE_FINISH;
            bits_btn_report_event(button, &result);

            button->state_bits = 0;  // 清零状态位
            button->current_state = BTN_STATE_IDLE;
            break;
        default:
            break;

    }

    // 更新上一次状态
    if(button->last_state != button->current_state)
    {
#if 0
        if(debug_printf)
            debug_printf("id[%d]:cur status:%d,last:%d\n", button->key_id, button->current_state, button->last_state);
#endif
        button->last_state = button->current_state;
    }
}

/**
  * @brief  Handle the button state based on the current mask and button mask.
  * @param  button: Pointer to the button object.
  * @param  current_mask: The current button mask.
  * @param  btn_mask: The button mask of the specific button.
  * @retval None
  */
static void handle_button_state(struct button_obj_t* button, button_mask_type_t current_mask, button_mask_type_t btn_mask)
{
    uint8_t pressed = (current_mask & btn_mask) == btn_mask? 1 : 0;
    update_button_state_machine(button, pressed);
}

/**
  * @brief  Dispatch and process combo buttons and generate a suppression mask.
  * @param  button: Pointer to the bits button object.
  * @param  suppression_mask: Pointer to store the suppression mask.
  * @retval None
  */
static void dispatch_combo_buttons(bits_button_t *button, button_mask_type_t *suppression_mask)
{
    if(button->btns_combo_cnt == 0) return;

    button_mask_type_t activated_mask = 0;

    for (uint16_t i = 0; i < button->btns_combo_cnt; i++)
    {
        uint16_t combo_index = button->combo_sorted_indices[i];
        button_obj_combo_t* combo = &button->btns_combo[combo_index];
        button_mask_type_t combo_mask = combo->combo_mask;

        // Check if the current combo button is covered by a more specific combo button
        if (activated_mask & combo_mask)
        {
            // Already covered, skip processing
            continue;
        }

        // Handle state transitions for this combo button
        handle_button_state(&combo->btn, button->current_mask, combo_mask);

        if ((button->current_mask & combo_mask) == combo_mask || combo->btn.state_bits)
        {
            // Mark the current combo button as activated
            activated_mask |= combo_mask;

            if (combo->suppress)
            {
                *suppression_mask |= combo_mask;
            }
        }
    }
}

/**
  * @brief  Dispatch and process unsuppressed individual buttons.
  * @param  button: Pointer to the bits button object.
  * @param  suppression_mask: The suppression mask.
  * @retval None
  */
static void dispatch_unsuppressed_buttons(bits_button_t *button, button_mask_type_t suppression_mask)
{
    // ​​Process Unsuppressed Individual Buttons
    for (size_t i = 0; i < button->btns_cnt; i++)
    {
        button_mask_type_t btn_mask = ((button_mask_type_t)1UL << i);

        // Skip individual buttons suppressed by combo buttons
        if (suppression_mask & btn_mask) {
            continue;
        }

        handle_button_state(&button->btns[i], button->current_mask, btn_mask);
    }
}

void bits_button_ticks(void)
{
    bits_button_t *button = &bits_btn_entity;
    uint32_t current_time = get_button_tick();

    button->btn_tick++;

    // Calculate button index
    button_mask_type_t new_mask = 0;
    for(size_t i = 0; i < button->btns_cnt; i++)
    {
        uint8_t read_gpio_level = button->_read_button_level(&button->btns[i]);

        if (read_gpio_level == button->btns[i].active_level)
        {
            new_mask |= ((button_mask_type_t)1UL << i);
        }
    }

    button->current_mask = new_mask;

    // State synchronization and debounce processing
    if(button->last_mask != new_mask)
    {
        button->state_entry_time = current_time;
        if(debug_printf)
            debug_printf("NEW MASK %d\n", new_mask);
        button->last_mask = new_mask;
    }

    uint32_t time_diff = current_time - button->state_entry_time;

    if(time_diff * BITS_BTN_TICKS_INTERVAL  < BITS_BTN_DEBOUNCE_TIME_MS)
    {
        return;
    }

    button_mask_type_t suppressed_mask = 0;

    dispatch_combo_buttons(button, &suppressed_mask);

    dispatch_unsuppressed_buttons(button, suppressed_mask);
}

/**
  * @brief  Debugging function, print the input decimal number in binary format.
  * @param  None.
  * @retval None
  */
 static void debug_print_binary(key_value_type_t num) {
    if(debug_printf == NULL)
        return;

    debug_printf("0b");
    int leading_zero = 1;

    for (int i = sizeof(key_value_type_t) * 8 - 1; i >= 0; i--) {
        if ((num >> i) & 1) {
            debug_printf("1");
            leading_zero = 0;
        } else if (!leading_zero) {
            debug_printf("0");
        }
    }

    if (leading_zero) {
        debug_printf("0");
    }

    debug_printf("\r\n");
}
